"""
Bridge Abutment Response Recorder Module

This module provides functions for recording time history responses of bridge abutments
during dynamic analysis using OpenSees recorders.

The module records:
- Abutment node displacements in X, Y, Z directions
"""

import os
import openseespy.opensees as ops
import numpy as np


def _setup_abutment_recorder(model, abutment_node, abutment_side, dt, output_dir, recorder_info):
    """
    Set up OpenSees recorders for a single abutment node.

    Args:
        model: Bridge model object
        abutment_node: Abutment node tag
        abutment_side: Abutment side ('left' or 'right')
        dt: Time step for recording
        output_dir: Directory to store recorder output files
        recorder_info: Dictionary to store recorder information

    Returns:
        dict: Updated recorder information
    """
    # Get abutment coordinates
    abutment_x = ops.nodeCoord(abutment_node, 1)
    abutment_y = ops.nodeCoord(abutment_node, 2)
    abutment_z = ops.nodeCoord(abutment_node, 3)

    # Create file name
    abutment_id = f"abutment_{abutment_side}_x{abutment_x:.1f}"
    disp_file = f"{output_dir}/abutment_disp_{abutment_id}.txt"

    # Set up displacement recorder for abutment node
    disp_recorder = ops.recorder('Node', '-file', disp_file, '-time', '-dT', dt,
                               '-node', abutment_node, '-dof', 1, 2, 3, 'disp')

    # Store recorder information
    recorder_info['abutments'][abutment_id] = {
        'abutment_side': abutment_side,
        'node_tag': abutment_node,
        'x_coord': abutment_x,
        'y_coord': abutment_y,
        'z_coord': abutment_z,
        'disp_file': disp_file
    }

    recorder_info['recorder_tags'].append(disp_recorder)

    return recorder_info


def setup_abutment_recorders(model, dt, output_dir='results'):
    """
    Set up OpenSees recorders for all abutments in the model.

    Args:
        model: Bridge model object
        dt: Time step for recording
        output_dir: Directory to store recorder output files

    Returns:
        dict: Dictionary with recorder information
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Check if there are abutments in the model
    if not model.abutments['nodes'] or len(model.abutments['nodes']) < 2:
        print("警告: 模型中没有桥台节点，无法设置桥台响应记录器")
        return {}

    # Dictionary to store recorder information
    recorder_info = {
        'abutments': {},
        'recorder_tags': []
    }

    # Set up recorders for left abutment
    left_abutment_node = model.abutments['nodes'][0]
    recorder_info = _setup_abutment_recorder(
        model, left_abutment_node, 'left', dt, output_dir, recorder_info
    )

    # Set up recorders for right abutment
    right_abutment_node = model.abutments['nodes'][1]
    recorder_info = _setup_abutment_recorder(
        model, right_abutment_node, 'right', dt, output_dir, recorder_info
    )

    print(f"已设置 {len(recorder_info['abutments'])} 个桥台响应记录器")
    return recorder_info


def read_abutment_displacement_data(recorder_info, output_dir='results'):
    """
    Read abutment displacement data from recorder files.

    Args:
        recorder_info: Dictionary with recorder information
        output_dir: Directory where recorder files are stored

    Returns:
        dict: Dictionary of abutment displacement data in the format {time: [abutment_data, ...]}
    """
    # Check if recorder_info is valid
    if not recorder_info or 'abutments' not in recorder_info:
        print("警告: 没有有效的记录器信息，无法读取桥台位移数据")
        return {}

    # Dictionary to store abutment displacement data
    abutment_disps = {}

    # Process each abutment in recorder_info
    for abutment_id, abutment_info in recorder_info['abutments'].items():
        # Get abutment information
        abutment_side = abutment_info['abutment_side']
        node_tag = abutment_info['node_tag']
        x_coord = abutment_info['x_coord']
        y_coord = abutment_info['y_coord']
        z_coord = abutment_info['z_coord']

        # Get displacement file path
        disp_file = abutment_info['disp_file']

        # Check if file exists
        if not os.path.exists(disp_file):
            print(f"警告: 文件 {disp_file} 不存在，跳过该桥台")
            continue

        try:
            # Read displacement data
            data = np.loadtxt(disp_file)

            # Check if data is empty
            if data.size == 0:
                print(f"警告: 文件 {disp_file} 中没有数据，跳过该桥台")
                continue

            # Handle single row data
            if data.ndim == 1:
                data = data.reshape(1, -1)

            # Process each time step
            for row in data:
                time = row[0]
                disp_x = row[1]
                disp_y = row[2]
                disp_z = row[3]

                # Initialize time step data if needed
                if time not in abutment_disps:
                    abutment_disps[time] = []

                # Add abutment data for this time step
                abutment_disps[time].append({
                    'abutment_side': abutment_side,
                    'node_tag': node_tag,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'z_coord': z_coord,
                    'disp_x': disp_x,
                    'disp_y': disp_y,
                    'disp_z': disp_z
                })

        except Exception as e:
            print(f"读取文件 {disp_file} 时出错: {e}")
            continue

    return abutment_disps
