import numpy as np


def calculate_pier_params(params):
    """计算桥墩相关参数"""
    update_concrete_params(params)
    update_steel_params(params)
    calculate_phyu(params)  # 截面极限曲率(1/m)
    return


def update_concrete_params(params):
    """更新混凝土参数"""
    for mat in params.concrete_materials.values():
        grade = mat["grade"]
        fcuk = int(grade[1:])  # 立方体抗压强度标准值 (MPa)
        fcu = 0.79 * fcuk      # 圆柱体抗压强度 (MPa)
        mat["Ec"] = 4700 * np.sqrt(fcu) # (MPa, 美国规范)
        mat["fc"] = fcu
        mat["ft"] = 0.79 * fcuk * 0.1  # (MPa)
        mat["Et"] = 0.125 * mat["Ec"]  # (MPa)
    # 计算核心区混凝土极限应变
    calculate_ecu(params)
    return


def  update_steel_params(params):
    """更新钢筋参数"""
    adjust_longitudinal_reinforcement_ratio(params, target_ratio=0.015)
    return


def calculate_ecu(params):
    # 计算体积配箍率 约0.005
    transverse_params = params.pier_section['transverse_bars']
    # 箍筋参数
    transverse_dia = transverse_params["diameter"]     # 箍筋直径(m)
    transverse_spacing = transverse_params["spacing"]  # 箍筋间距(m)

    # 计算约束效应
    D = params.pier_section["diameter"]            # 桥墩直径(m)
    cover = params.pier_section["concrete_cover"]  # 保护层厚度(m)
    D_core = D - 2 * cover                         # 核心区直径(m)

    # 计算体积配箍率
    if transverse_params["configuration"] == "spiral":  # 螺旋箍筋
        rho_s = 4 * transverse_dia**2 / (D_core * transverse_spacing)
    else:  # 环形箍筋
        rho_s = 2 * transverse_dia**2 / (D_core * transverse_spacing)
    params.pier_section['transverse_bars']['rho'] = rho_s
    print(f"桥墩体积配箍率: {rho_s*100:.2f}%")
    assert rho_s >= 0.004

    fkh = params.steel_materials["transverse"]["fy"]
    fcck = 1.25 * params.concrete_materials["core"]["fc"]/0.79
    ecu = 0.004 + 1.4*rho_s*fkh*0.09/fcck
    print(f"约束混凝土极限应变: {ecu:.4f}")
    params.concrete_materials["core"]["ecu"] = ecu
    return ecu


def adjust_longitudinal_reinforcement_ratio(params, target_ratio=0.015):
    """
    调整桥墩纵向钢筋配筋率至目标值（默认1.5%）

    通过调整钢筋直径和数量来实现目标配筋率，并更新params中的相关参数

    参数:
        params: 桥梁参数对象
        target_ratio: 目标配筋率，默认为0.015（1.5%）

    返回:
        float: 调整后的实际配筋率
    """
    # 获取当前参数
    D = params.pier_section["diameter"]            # 桥墩直径(m)

    # 获取纵向钢筋参数
    long_bars = params.pier_section['longitudinal_bars']
    current_dia = long_bars['diameter']            # 当前钢筋直径(m)
    current_num = long_bars['number']              # 当前钢筋数量

    # 计算当前配筋率
    A_concrete = np.pi * (D/2)**2                  # 混凝土截面面积(m²)
    A_steel = current_num * np.pi * (current_dia/2)**2  # 钢筋总面积(m²)
    current_ratio = A_steel / A_concrete

    # print(f"当前桥墩纵向钢筋配筋率: {current_ratio*100:.2f}%")
    # print(f"目标桥墩纵向钢筋配筋率: {target_ratio*100:.2f}%")

    # 如果当前配筋率已经接近目标值，则不需要调整
    if abs(current_ratio - target_ratio) < 0.0005:  # 允许0.05%的误差
        # print("当前配筋率已接近目标值，无需调整")
        params.pier_section['longitudinal_bars']['rho'] = current_ratio
        print(f"桥墩纵向钢筋配筋率: {current_ratio*100:.2f}%")
        return current_ratio

    # 计算所需的钢筋总面积
    required_A_steel = A_concrete * target_ratio

    # 调整策略：优先调整钢筋直径，如果直径调整后仍不满足要求，则调整钢筋数量
    # 计算新的钢筋直径（保持数量不变）
    new_dia = np.sqrt(required_A_steel / (current_num * np.pi)) * 2

    # 检查新直径是否在合理范围内（12mm-40mm）
    if 0.012 <= new_dia <= 0.040:
        # 直径调整在合理范围内，使用新直径
        params.pier_section['longitudinal_bars']['diameter'] = new_dia
        # print(f"调整钢筋直径: {current_dia*1000:.1f}mm -> {new_dia*1000:.1f}mm (保持数量: {current_num}根)")
    else:
        # 直径超出合理范围，保持合理的直径并调整数量
        if new_dia < 0.012:
            new_dia = 0.012  # 最小直径12mm
        elif new_dia > 0.040:
            new_dia = 0.040  # 最大直径40mm

        # 计算所需的钢筋数量
        new_num = int(np.ceil(required_A_steel / (np.pi * (new_dia/2)**2)))

        # 确保钢筋数量是偶数（便于对称布置）
        if new_num % 2 != 0:
            new_num += 1

        params.pier_section['longitudinal_bars']['diameter'] = new_dia
        params.pier_section['longitudinal_bars']['number'] = new_num
        # print(f"调整钢筋: 直径 {current_dia*1000:.1f}mm -> {new_dia*1000:.1f}mm, 数量 {current_num}根 -> {new_num}根")

    # 计算调整后的实际配筋率
    new_dia = params.pier_section['longitudinal_bars']['diameter']
    new_num = params.pier_section['longitudinal_bars']['number']
    new_A_steel = new_num * np.pi * (new_dia/2)**2
    actual_ratio = new_A_steel / A_concrete

    print(f"桥墩纵向钢筋配筋率: {actual_ratio*100:.2f}%")

    # 更新params中的配筋率参数（如果需要）
    params.pier_section['longitudinal_bars']['rho'] = actual_ratio

    return actual_ratio


def calculate_phyu(params):
    """计算桥墩截面的极限曲率(1/m)"""
    fck = 40 * 1e6  # (N/m²)
    Ag = np.pi * (params.pier_section['diameter']/2)**2  # (m²)
    ecu = params.concrete_materials["core"]["ecu"]

    # 计算单根柱截面实际所受轴力P
    lspan = params.span_lengths
    weight = params.super_weight
    # 排除桥台部分
    weight *= 1 - 0.5 * (lspan[0]+lspan[-1]) / np.sum(lspan)
    # 考虑盖梁重量
    weight += params.cap_weight
    num_piers = (params.num_spans-1) * params.num_piers_transverse
    P = weight / num_piers  # N

    # 计算极限曲率系数 (根据规范公式)
    es = params.steel_materials["longitudinal"]["eu"]
    D = params.pier_section['diameter']  # 桥墩直径 (m)
    phiu1 = (2.826e-3 + 6.85*ecu - (8.575e-3 + 18.638*ecu) * P/(fck*Ag)) / D
    phiu2 = (1.635e-3 + 1.179*es + (28.739*es*es + 0.656*es + 0.01) * P/(fck*Ag)) / D
    phiu = np.minimum(phiu1, phiu2)
    params.pier_section['phiu'] = phiu
    return phiu